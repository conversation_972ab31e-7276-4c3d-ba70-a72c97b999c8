package fm.lizhi.ocean.wavecenter.web.module.live.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInRoomStatisticBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserRecordSumBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserSumBean;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInRoomStatistic;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInRoomStatistic;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.web.common.PageVO;
import fm.lizhi.ocean.wavecenter.web.module.file.handler.DynamicColTable;
import fm.lizhi.ocean.wavecenter.web.module.live.constant.CheckExportSheetDataType;
import fm.lizhi.ocean.wavecenter.web.module.live.constant.CheckInMetricsEnum;
import fm.lizhi.ocean.wavecenter.web.module.live.model.param.ExportCheckInRoomDetailParam;
import fm.lizhi.ocean.wavecenter.web.module.live.model.param.ExportCheckInRoomSheetParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CheckInExportHandler {

    /**
     * 获取麦序福利厅详情 行合计
     * @param list
     * @return
     */
    public WaveCheckInRoomStatisticBean sumWaveCheckInRoomStatisticList(List<WaveCheckInRoomStatisticBean> list) {
        WaveCheckInRoomStatisticBean total = new WaveCheckInRoomStatisticBean();
        if (CollectionUtils.isEmpty(list)) {
            return total;
        }

        // 1. 初始化汇总对象
        WaveCheckInUserSumBean sumBean = new WaveCheckInUserSumBean();


        total.setSum(sumBean);

        // 2. 创建按time分组的detail合并容器
        Map<String, WaveCheckInUserRecordSumBean> mergedDetails = new LinkedHashMap<>();

        // 3. 遍历所有记录
        for (WaveCheckInRoomStatisticBean bean : list) {
            if (bean == null || bean.getSum() == null) {
                continue;
            }

            // 4. 累加sum字段数值
            WaveCheckInUserSumBean currentSum = bean.getSum();
            sumBean.setScheduledCnt(sumBean.getScheduledCnt() + currentSum.getScheduledCnt());
            sumBean.setSeatCnt(sumBean.getSeatCnt() + currentSum.getSeatCnt());
            sumBean.setSeatCharm(sumBean.getSeatCharm() + currentSum.getSeatCharm());
            sumBean.setHostCnt(sumBean.getHostCnt() + currentSum.getHostCnt());
            sumBean.setHostCharmSum(sumBean.getHostCharmSum() + currentSum.getHostCharmSum());
            sumBean.setSumCharm(sumBean.getSumCharm() + currentSum.getSumCharm());
            sumBean.setSumIncome(sumBean.getSumIncome() + currentSum.getSumIncome());
            sumBean.setNotDoneScore(sumBean.getNotDoneScore() + currentSum.getNotDoneScore());
            sumBean.setLightGiftAmount(sumBean.getLightGiftAmount() + currentSum.getLightGiftAmount());
            sumBean.setAllMicGiftAmount(sumBean.getAllMicGiftAmount() + currentSum.getAllMicGiftAmount());
            sumBean.setDayMicAmount(sumBean.getDayMicAmount() + currentSum.getDayMicAmount());

            // 5. 合并detail字段
            if (CollectionUtils.isNotEmpty(bean.getDetail())) {
                for (WaveCheckInUserRecordSumBean detail : bean.getDetail()) {
                    if (detail == null || StringUtils.isBlank(detail.getTime())) {
                        continue;
                    }
                    WaveCheckInUserRecordSumBean detailSum = mergedDetails.computeIfAbsent(detail.getTime(), k -> new WaveCheckInUserRecordSumBean());
                    detailSum.setTime(detail.getTime());
                    detailSum.setCharm(detailSum.getCharm() + detail.getCharm());
                    detailSum.setIncome(detailSum.getIncome() + detail.getIncome());
                }
            }
        }

        // 6. 设置合并后的detail
        total.setDetail(new ArrayList<>(mergedDetails.values()));
        return total;
    }


    private String sanitizeFileName(String originName, String defaultValue) {
        // 替换非法的文件名字符
        // \（反斜杠）、/（正斜杠）、:（冒号）、*（星号）、?（问号）、"（双引号）、<（小于）、>（大于）、|（竖线） 是windows系统禁止的字符
        //  （空格）、_（下划线）、-（连字符）、.（点号）是不能用于文件名开头或结尾的字符
        // @（at）、#（井号）、$（美元符号）、&（和号）、;（分号）是纯人工额外添加的禁止字符避免其他特殊情况
        String sanitizedName = originName.replaceAll("[\\\\/:*?\"<>|\\s._@#$&;-]", StringUtils.EMPTY);
        if (StringUtils.isBlank(sanitizedName)) {
            return defaultValue;
        }
        return sanitizedName;
    }

    public String sanitizeFamilyFileName(FamilyBean familyBean) {
        return sanitizeFileName(familyBean.getFamilyName(), "导出全部") + "_工会打卡明细表";
    }

    public String sanitizeRoomFileName(UserBean userBean) {
        return sanitizeFileName(userBean.getName(), userBean.getBand()) + "_厅打卡明细表";
    }

    public String sanitizePlayerFileName(UserBean userBean) {
        return sanitizeFileName(userBean.getName(), userBean.getBand()) + "_主播打卡明细表";
    }

    private String sanitizeSheetName(String sheetName, String defaultValue) {
        // 替换非法的工作表名字符
        // /（正斜杠）、\（反斜杠）、?（问号）、*（星号）、:（冒号）、[（左中括号）、]（右中括号）、'（单引号）
        // 详见 https://support.microsoft.com/en-us/office/rename-a-worksheet-3f1f7148-ee83-404d-8ef0-9ff99fbad1f9
        String sanitizedName = sheetName.replaceAll("[/\\\\?*:\\[\\]']", StringUtils.EMPTY);
        if (StringUtils.isBlank(sanitizedName)) {
            return defaultValue;
        }
        return sanitizedName;
    }

    private String joinSheetName(String sheetPrefix, String sheetSuffix) {
        // 详见 https://support.microsoft.com/en-us/office/rename-a-worksheet-3f1f7148-ee83-404d-8ef0-9ff99fbad1f9
        int maxSheetNameLength = 31;
        int availablePrefixLength = Math.max(maxSheetNameLength - sheetSuffix.length(), 0);
        String retainPrefix = availablePrefixLength >= sheetPrefix.length() ? sheetPrefix : sheetPrefix.substring(0, availablePrefixLength);
        return retainPrefix + sheetSuffix;
    }

    public String sanitizeRoomSumSheetName(UserBean njUserBean) {
        if (njUserBean == null) {
            return "xx_厅打卡总计";
        }
        String sanitizedName = sanitizeSheetName(njUserBean.getName(), njUserBean.getBand());
        return joinSheetName(sanitizedName, "_厅打卡总计");
    }


    public String sanitizeRoomCharmSheetName(UserBean njUserBean) {
        if (njUserBean == null) {
            return "xx_厅24小时魅力值明细";
        }
        String sanitizedName = sanitizeSheetName(njUserBean.getName(), njUserBean.getBand());
        return joinSheetName(sanitizedName, "_厅24小时魅力值明细");
    }

    private String sanitizeRoomIncomeSheetName(UserBean njUserBean) {
        if (njUserBean == null) {
            return "xx_厅钻石明细";
        }
        String sanitizedName = sanitizeSheetName(njUserBean.getName(), njUserBean.getBand());
        return joinSheetName(sanitizedName, "_厅钻石明细");
    }

    public String sanitizePlayerCharmSheetName(UserBean njUserBean) {
        if (njUserBean == null) {
            return "xx_主播魅力值明细";
        }
        String sanitizedName = sanitizeSheetName(njUserBean.getName(), njUserBean.getBand());
        return joinSheetName(sanitizedName, "_主播魅力值明细");
    }

    public String sanitizePlayerIncomeSheetName(UserBean njUserBean) {
        if (njUserBean == null) {
            return "xx_主播钻石明细";
        }
        String sanitizedName = sanitizeSheetName(njUserBean.getName(), njUserBean.getBand());
        return joinSheetName(sanitizedName, "_主播钻石明细");
    }

    public void appendCharmDetailColumns(DynamicColTable.Row<Object> row, List<WaveCheckInUserRecordSumBean> detailBeans) {
        if (CollectionUtils.isEmpty(detailBeans)) {
            return;
        }
        // 魅力值收入明细
        for (WaveCheckInUserRecordSumBean detailBean : detailBeans) {
            row.putCol(detailBean.getTime(), Objects.toString(detailBean.getCharm(), StringUtils.EMPTY));
        }
    }

    public void appendIncomeDetailColumns(DynamicColTable.Row<Object> row, List<WaveCheckInUserRecordSumBean> sumBeans) {
        if (CollectionUtils.isEmpty(sumBeans)) {
            return;
        }
        // 钻石收入明细
        for (WaveCheckInUserRecordSumBean sumBean : sumBeans) {
            row.putCol(sumBean.getTime(), Objects.toString(sumBean.getIncome(), StringUtils.EMPTY));
        }
    }

    public void appendCharmStatisticSum(DynamicColTable.Row<Object> row, WaveCheckInUserSumBean sum, boolean containsCharmDiff) {
        if (sum == null) {
            return;
        }
        // 合计魅力值
        row.putCol("合计魅力值", Objects.toString(sum.getSumCharm(), StringUtils.EMPTY));
        // 调账
        if (containsCharmDiff) {
            row.putCol("调账结果", Objects.toString(sum.getCharmDiffIn() + "\n" + sum.getCharmDiffOut(), StringUtils.EMPTY));
        }
        // 未完成任务
        row.putCol("未完成任务", Objects.toString(sum.getNotDoneScoreDetail(), StringUtils.EMPTY));
        // 有效麦序
        row.putCol("有效麦序", Objects.toString(sum.getSeatCnt(), StringUtils.EMPTY));
        // 有效麦序魅力值
        row.putCol("有效麦序魅力值", Objects.toString(sum.getSeatCharm(), StringUtils.EMPTY));
        // 排档数
        row.putCol("排档数", Objects.toString(sum.getScheduledCnt(), StringUtils.EMPTY));
        // 主持档数
        row.putCol("主持档数", Objects.toString(sum.getHostCnt(), StringUtils.EMPTY));
        // 主持档魅力值
        row.putCol("主持档魅力值", Objects.toString(sum.getHostCharmSum(), StringUtils.EMPTY));
        // 收光记录
        row.putCol("收光记录", Objects.toString(sum.getLightGift(), StringUtils.EMPTY));
        // 收光奖励（元）
        row.putCol("收光奖励（元）", Objects.toString(sum.getLightGiftAmount(), StringUtils.EMPTY));
        // 全麦记录
        row.putCol("全麦记录", Objects.toString(sum.getAllMicGift(), StringUtils.EMPTY));
        // 全麦奖励
        row.putCol("全麦奖励（元）", Objects.toString(sum.getAllMicGiftAmount(), StringUtils.EMPTY));
        // 日麦序奖励
        row.putCol("日麦序奖励（元）", Objects.toString(sum.getDayMicAmount(), StringUtils.EMPTY));
        // 私信人数
        row.putCol("私信人数", Objects.toString(sum.getChatUserCnt(), StringUtils.EMPTY));
        // 私信回复率
        row.putCol("私信回复率", Objects.toString(sum.getChatReplyRate(), StringUtils.EMPTY));

    }

    public void appendCharmStatisticSumByHour(DynamicColTable.Row<Object> row, WaveCheckInUserSumBean sum, boolean containsCharmDiff, WaveCheckInUserBean host, Long currentDateTime) {
        if (sum == null) {
            return;
        }

        // 时间
        String time = DateUtil.hour(new Date(currentDateTime), true) + "-" + DateUtil.hour(DateUtil.offsetHour(new Date(currentDateTime), 1), true);
        row.putCol("时间", Objects.toString(time, StringUtils.EMPTY));
        if (host != null){
            // 主持
            row.putCol("主持ID", Objects.toString(host.getBand(), StringUtils.EMPTY));
            row.putCol("主持", Objects.toString(host.getName(), StringUtils.EMPTY));
        }

        // 魅力值
        row.putCol("魅力值", Objects.toString(sum.getSumCharm(), StringUtils.EMPTY));
        // 调账
        if (containsCharmDiff) {
            row.putCol("调账结果", Objects.toString(sum.getCharmDiffIn() + "\n" + sum.getCharmDiffOut(), StringUtils.EMPTY));
        }

        // 私信人数
        row.putCol("私信人数", Objects.toString(sum.getChatUserCnt(), StringUtils.EMPTY));
        // 排档数
        row.putCol("排档数", Objects.toString(sum.getScheduledCnt(), StringUtils.EMPTY));
        // 麦序
        row.putCol("麦序", Objects.toString(sum.getSeatCnt(), StringUtils.EMPTY));
        // 备注
        row.putCol("备注", Objects.toString(sum.getRemark(), StringUtils.EMPTY));
    }

    public void appendIncomeStatisticSum(DynamicColTable.Row<Object> row, WaveCheckInUserSumBean sum) {
        if (sum == null) {
            return;
        }
        // 合计钻石值
        row.putCol("合计钻石值", Objects.toString(sum.getSumIncome(), StringUtils.EMPTY));
    }


    public static void main(String[] args) {
//        String dataJson = "[{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":23,\"income\":8,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":50368,\"income\":162,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180005908\",\"id\":1369753360190123266,\"name\":\"的话堵得慌北戴河读读\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/06/13/3013806376780845570.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*162\",\"lightGiftAmount\":162,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":50393,\"sumIncome\":172}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":22,\"income\":7,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":48928,\"income\":157,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180005216\",\"id\":1369754036646975490,\"name\":\"长昵称测试长昵称测试\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/06/13/3013806963043913730.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*157\",\"lightGiftAmount\":157,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":48952,\"sumIncome\":166}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":1,\"income\":1,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":864,\"income\":3,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180006592\",\"id\":1376213950928013954,\"name\":\"A7\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041049758065315842.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*3\",\"lightGiftAmount\":3,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":867,\"sumIncome\":6}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":1,\"income\":1,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":576,\"income\":2,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180005976\",\"id\":1376214187151217794,\"name\":\"A8\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041050032943223298.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*2\",\"lightGiftAmount\":2,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":579,\"sumIncome\":5}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":1,\"income\":1,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":19296,\"income\":67,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180006769\",\"id\":1376216504286086018,\"name\":\"A9\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041051519001909250.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*67\",\"lightGiftAmount\":67,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":19299,\"sumIncome\":70}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":1,\"income\":1,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":19008,\"income\":66,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180006345\",\"id\":1376216727624387714,\"name\":\"A10\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041051742340209154.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*66\",\"lightGiftAmount\":66,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":19011,\"sumIncome\":69}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":3,\"income\":3,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":68225,\"income\":225,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"99998888\",\"id\":1386652551113644802,\"name\":\"不睡觉啦\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041077441277039106.jpg\"},\"sum\":{\"allMicGift\":\"100*2\",\"allMicGiftAmount\":84,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*224\",\"lightGiftAmount\":224,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":68230,\"sumIncome\":230}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":1,\"income\":1,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":19008,\"income\":66,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180006464\",\"id\":1391990761670705410,\"name\":\"A11\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041051942056188930.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*66\",\"lightGiftAmount\":66,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":19011,\"sumIncome\":69}},{\"detail\":[{\"charm\":2,\"income\":2,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":1,\"income\":1,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":5473,\"income\":20,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180006374\",\"id\":1396994907524556930,\"name\":\"A6\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2023/11/07/3041049472449988098.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":0,\"lightGift\":\"100*19\",\"lightGiftAmount\":19,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":5476,\"sumIncome\":23}},{\"detail\":[{\"charm\":0,\"income\":0,\"time\":\"02/24\"},{\"charm\":0,\"income\":0,\"time\":\"02/25\"},{\"charm\":0,\"income\":0,\"time\":\"02/26\"},{\"charm\":0,\"income\":0,\"time\":\"02/27\"},{\"charm\":0,\"income\":0,\"time\":\"02/28\"},{\"charm\":0,\"income\":0,\"time\":\"03/01\"},{\"charm\":0,\"income\":0,\"time\":\"03/02\"}],\"player\":{\"band\":\"180006278\",\"id\":1386652918333353474,\"name\":\"还好哈哈哈\",\"photo\":\"https://cdnoffice.lizhi.fm/user/2019/10/10/2764783519893950466.jpg\"},\"sum\":{\"allMicGift\":\"\",\"allMicGiftAmount\":0,\"dayMicAmount\":0,\"hostCharmSum\":0,\"hostCnt\":1,\"lightGift\":\"\",\"lightGiftAmount\":0,\"notDoneScore\":0,\"scheduledCnt\":0,\"seatCharm\":0,\"seatCnt\":0,\"sumCharm\":0,\"sumIncome\":0}}]";
//        CheckInController checkInController = new CheckInController();
//        List<WaveCheckInRoomStatisticBean> list = JsonUtil.loadsArray(dataJson, WaveCheckInRoomStatisticBean.class);

//        List<DynamicColTable.Row<Object>> charmRow = checkInController.buildRow(list, CheckInMetricsEnum.CHARM);
//        List<DynamicColTable.Row<Object>> incomeRow = checkInController.buildRow(list, CheckInMetricsEnum.INCOME);
//
//        WaveCheckInRoomStatisticBean sum = checkInExportHandler.sumWaveCheckInRoomStatisticList(list);
//        System.out.println("sum = " + sum);


//        DateTime currentStartTime = DateUtil.beginOfDay(new Date(1749625200000L));
//        while (currentStartTime.getTime() <= (DateUtil.isSameDay(currentStartTime, new Date()) ?  new Date().getTime() : DateUtil.endOfDay(new Date(1749625200000L)).getTime())){
////            req.setStartDate(currentStartTime.getTime());
////            req.setEndDate(DateUtil.endOfHour(currentStartTime).getTime());
//
////            List<WaveCheckInRoomStatisticBean> list = getExportCheckInRoomStatistic(req, false);
//
//            log.info("currentStartTime: {}, currentEndTime: {}", currentStartTime, DateUtil.endOfHour(currentStartTime));
//            // 往下增加一个小时
//            currentStartTime = currentStartTime.offset(DateField.HOUR, 1);
//        }

        Long current = new Date().getTime();
        String i = DateUtil.hour(new Date(current), true) + "-" + DateUtil.hour(DateUtil.offsetHour(new Date(current), 1), true);
        log.info("i = " + i);

    }

    /**
     * 小时类型的导出，和日/周不太一样
     * 传进来的参数是小时的，但是实际导出要当天的
     * @param sheetParam
     * @param familyId
     * @return
     */
    private PageVO<DynamicColTable.Row<Object>> buildExportCheckInRoomDetailRowsByHour(ExportCheckInRoomSheetParam sheetParam, Long familyId) {
        Result<Long> roomIdByNjId = getRoomIdByNjId(sheetParam.getAppId(), sheetParam.getNjId());
        if (RpcResult.isFail(roomIdByNjId)) {
            return PageVO.empty();
        }
        Long roomId = roomIdByNjId.target();
        RequestGetCheckInRoomStatistic req = checkInConverter.toRequestGetCheckInRoomStatistic(sheetParam, roomId, familyId);
        Map<Long, ResponseGetCheckInRoomStatistic> checkInRoomStatisticBeanMap = new HashMap<>();

        // 根据传入的时间参数，计算当天 0 点到当前时间的时间段，按小时划分
        DateTime currentStartTime = DateUtil.beginOfDay(new Date(sheetParam.getStartDate()));
        DateTime endTime = DateUtil.isSameDay(currentStartTime, new Date()) ? new DateTime() : DateUtil.endOfDay(new Date(sheetParam.getStartDate()));
        while (currentStartTime.getTime() <= endTime.getTime()){
            req.setStartDate(currentStartTime.getTime());
            req.setEndDate(DateUtil.endOfHour(currentStartTime).getTime());

            Result<ResponseGetCheckInRoomStatistic> result = waveCheckInDataService.getCheckInRoomStatistic(req);
            if (RpcResult.isFail(result)) {
                log.warn("getCheckInRoomStatistic fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            } else {
                ResponseGetCheckInRoomStatistic target = result.target();
                if (target != null && CollUtil.isNotEmpty(target.getList())){
                    checkInRoomStatisticBeanMap.put(currentStartTime.getTime(), target);
                }else {
                    log.info("getCheckInRoomStatistic empty, req: {}", req);
                }
            }

            // 往下增加一个小时
            currentStartTime = currentStartTime.offset(DateField.HOUR, 1);
        }
        if (MapUtils.isEmpty(checkInRoomStatisticBeanMap)) {
            // 构建一个默认行，避免 sheet 丢失
            ResponseGetCheckInRoomStatistic defaultDate = new ResponseGetCheckInRoomStatistic();
            WaveCheckInRoomStatisticBean bean = new WaveCheckInRoomStatisticBean();
            WaveCheckInUserBean sumPlayer = new WaveCheckInUserBean();
            sumPlayer.setId(-1L);
            sumPlayer.setName("");
            sumPlayer.setBand("");
            bean.setPlayer(sumPlayer);
            defaultDate.setList(CollUtil.newArrayList(bean));
            checkInRoomStatisticBeanMap.put(sheetParam.getStartDate(), defaultDate);
        }

        List<DynamicColTable.Row<Object>> rows = new ArrayList<>();
        for (Map.Entry<Long, ResponseGetCheckInRoomStatistic> entry : checkInRoomStatisticBeanMap.entrySet()) {
            ResponseGetCheckInRoomStatistic response = entry.getValue();
            if (response == null){
                continue;
            }

            List<WaveCheckInRoomStatisticBean> list = response.getList();
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }

            boolean containsCharmDiff = list.stream()
                    .anyMatch(bean -> {
                        WaveCheckInUserSumBean sum = bean.getSum();
                        if (sum == null) {
                            return false;
                        }
                        return sum.getCharmDiffIn() > 0 || sum.getCharmDiffOut() > 0;
                    });

            // 构建行数据
            for (WaveCheckInRoomStatisticBean statisticBean : list) {
                DynamicColTable.Row<Object> row = new DynamicColTable.Row<>();
                // 昵称
                row.putFreezeCol(Objects.toString(statisticBean.getPlayer().getName(), StringUtils.EMPTY));
                // 波段号
                row.putFreezeCol(Objects.toString(statisticBean.getPlayer().getBand(), StringUtils.EMPTY));
                WaveCheckInUserSumBean sum = statisticBean.getSum();
                checkInExportHandler.appendCharmStatisticSumByHour(row, sum, containsCharmDiff, response.getHost(), entry.getKey());
                rows.add(row);
            }

        }

        return PageVO.of(rows.size(), rows);
    }

    private PageVO<DynamicColTable.Row<Object>> buildExportCheckInRoomDetailRowsByDay(ExportCheckInRoomSheetParam sheetParam, Long familyId) {
        Result<Long> roomIdByNjId = getRoomIdByNjId(sheetParam.getAppId(), sheetParam.getNjId());
        if (RpcResult.isFail(roomIdByNjId)) {
            return PageVO.empty();
        }
        Long roomId = roomIdByNjId.target();
        RequestGetCheckInRoomStatistic req = checkInConverter.toRequestGetCheckInRoomStatistic(sheetParam, roomId, familyId);
        List<WaveCheckInRoomStatisticBean> list = getExportCheckInRoomStatistic(req, true);

        if (CollectionUtils.isEmpty(list)) {
            return PageVO.empty();
        }

        boolean containsCharmDiff = list.stream()
                .anyMatch(bean -> {
                    WaveCheckInUserSumBean sum = bean.getSum();
                    if (sum == null) {
                        return false;
                    }
                    return sum.getCharmDiffIn() > 0 || sum.getCharmDiffOut() > 0;
                });
        // 构建行数据
        List<DynamicColTable.Row<Object>> rows = new ArrayList<>();
        for (WaveCheckInRoomStatisticBean statisticBean : list) {
            DynamicColTable.Row<Object> row = new DynamicColTable.Row<>();
            // 昵称
            row.putFreezeCol(Objects.toString(statisticBean.getPlayer().getName(), StringUtils.EMPTY));
            // 波段号
            row.putFreezeCol(Objects.toString(statisticBean.getPlayer().getBand(), StringUtils.EMPTY));
            List<WaveCheckInUserRecordSumBean> detail = statisticBean.getDetail();
            WaveCheckInUserSumBean sum = statisticBean.getSum();
            if (sheetParam.getSheetDataType().equals(CheckExportSheetDataType.SUM)){
                // 其他统计字段
                checkInExportHandler.appendCharmStatisticSum(row, sum, containsCharmDiff);
            }else if (sheetParam.getSheetDataType().equals(CheckExportSheetDataType.CHARM_DETAIL)){
                // 魅力值收入明细
                checkInExportHandler.appendCharmDetailColumns(row, detail);
            }
            rows.add(row);
        }
        return PageVO.of(rows.size(), rows);
    }

    /**
     * 导出麦序福利厅明细-周维度
     */
    private PageVO<DynamicColTable.Row<Object>> buildExportCheckInRoomDetailRowsByWeek(ExportCheckInRoomSheetParam sheetParam, Long familyId) {
        Result<Long> roomIdByNjId = getRoomIdByNjId(sheetParam.getAppId(), sheetParam.getNjId());
        if (RpcResult.isFail(roomIdByNjId)) {
            return PageVO.empty();
        }
        Long roomId = roomIdByNjId.target();
        RequestGetCheckInRoomStatistic req = checkInConverter.toRequestGetCheckInRoomStatistic(sheetParam, roomId, familyId);
        List<WaveCheckInRoomStatisticBean> list = getExportCheckInRoomStatistic(req, true);

        if (CollectionUtils.isEmpty(list)) {
            return PageVO.empty();
        }
        boolean containsCharmDiff = list.stream()
                .anyMatch(bean -> {
                    WaveCheckInUserSumBean sum = bean.getSum();
                    if (sum == null) {
                        return false;
                    }
                    return sum.getCharmDiffIn() > 0 || sum.getCharmDiffOut() > 0;
                });
        // 构建行数据
        List<DynamicColTable.Row<Object>> rows = new ArrayList<>();
        for (WaveCheckInRoomStatisticBean statisticBean : list) {
            DynamicColTable.Row<Object> row = new DynamicColTable.Row<>();
            // 昵称
            row.putFreezeCol(Objects.toString(statisticBean.getPlayer().getName(), StringUtils.EMPTY));
            // 波段号
            row.putFreezeCol(Objects.toString(statisticBean.getPlayer().getBand(), StringUtils.EMPTY));
            List<WaveCheckInUserRecordSumBean> detail = statisticBean.getDetail();
            WaveCheckInUserSumBean sum = statisticBean.getSum();
            // 魅力值收入明细
            checkInExportHandler.appendCharmDetailColumns(row, detail);
            // 其他统计字段
            checkInExportHandler.appendCharmStatisticSum(row, sum, containsCharmDiff);
            rows.add(row);
        }
        return PageVO.of(rows.size(), rows);
    }

    /**
     * 获取导出麦序福利厅明细数据
     */
    private List<WaveCheckInRoomStatisticBean> getExportCheckInRoomStatistic(RequestGetCheckInRoomStatistic req, boolean containsSum) {
        List<WaveCheckInRoomStatisticBean> list;
        Result<ResponseGetCheckInRoomStatistic> result = waveCheckInDataService.getCheckInRoomStatistic(req);
        if (RpcResult.isFail(result)) {
            log.warn("getCheckInRoomStatistic fail, req: {}, rCode: {}, message: {}", req, result.rCode(), result.getMessage());
            list = new ArrayList<>();
        } else {
            list = result.target().getList();
            if (containsSum) {
                //合计行
                WaveCheckInRoomStatisticBean rowSum = checkInExportHandler.sumWaveCheckInRoomStatisticList(list);
                WaveCheckInUserBean sumPlayer = new WaveCheckInUserBean();
                sumPlayer.setId(-1L);
                sumPlayer.setName("合计");
                sumPlayer.setBand("");
                rowSum.setPlayer(sumPlayer);
                list.add(0, rowSum);
            }
        }
        return list;
    }


    /**
     * 获取厅打卡明细 sheet 导出参数
     *
     * @param param 导出参数
     * @param appId appId
     * @param njIdUserBeanMap njId -> UserBean
     * @return sheetId -> ExportCheckInRoomSheetParam
     */
    private TreeMap<Long, ExportCheckInRoomSheetParam> getExportCheckInRoomSheetParamMap(ExportCheckInRoomDetailParam
                                                                                                 param, int appId, TreeMap<Long, UserBean> njIdUserBeanMap) {
        TreeMap<Long, ExportCheckInRoomSheetParam> sheetIdParamMap = new TreeMap<>();
        AtomicLong sheetIdGenerator = new AtomicLong();
        if (param.getDateType().equals(CheckInDateTypeEnum.HOUR) || param.getDateType().equals(CheckInDateTypeEnum.WEEK)){
            // 如果是小时和周维度，sheet 仅展示厅昵称即可
            for (Map.Entry<Long, UserBean> entry : njIdUserBeanMap.entrySet()) {
                Long njId = entry.getKey();
                UserBean njUserBean = njIdUserBeanMap.get(njId);
                String sheetName = njUserBean.getName();
                ExportCheckInRoomSheetParam exportParam = checkInConverter.toExportCheckInRoomSheetParam(
                        param, appId, njId, sheetName, CheckExportSheetDataType.SUM);
                sheetIdParamMap.put(sheetIdGenerator.getAndIncrement(), exportParam);
            }
        } else if (param.getDateType().equals(CheckInDateTypeEnum.DAY)) {
            // 如果是天，展示厅昵称打卡总计、厅昵称24小时魅力值明细
            for (Map.Entry<Long, UserBean> entry : njIdUserBeanMap.entrySet()) {
                Long njId = entry.getKey();
                UserBean njUserBean = njIdUserBeanMap.get(njId);
                String sumSheetName = checkInExportHandler.sanitizeRoomSumSheetName(njUserBean);
                ExportCheckInRoomSheetParam exportParam = checkInConverter.toExportCheckInRoomSheetParam(
                        param, appId, njId, sumSheetName, CheckExportSheetDataType.SUM);
                String charmSheetName = checkInExportHandler.sanitizeRoomCharmSheetName(njUserBean);
                ExportCheckInRoomSheetParam charmExportParam = checkInConverter.toExportCheckInRoomSheetParam(
                        param, appId, njId, charmSheetName, CheckExportSheetDataType.CHARM_DETAIL);
                sheetIdParamMap.put(sheetIdGenerator.getAndIncrement(), charmExportParam);
                sheetIdParamMap.put(sheetIdGenerator.getAndIncrement(), exportParam);
            }
        } else {
            log.warn("不支持的导出类型: {}", param.getDateType());
        }

        return sheetIdParamMap;
    }

    private List<DynamicColTable.Row<Object>> buildRow(List<WaveCheckInRoomStatisticBean> templist, CheckInMetricsEnum metrics){
        ArrayList<WaveCheckInRoomStatisticBean> list = new ArrayList<>(templist);
        //合计行
        WaveCheckInRoomStatisticBean rowSum = checkInExportHandler.sumWaveCheckInRoomStatisticList(list);
        WaveCheckInUserBean sumPlayer = new WaveCheckInUserBean();
        sumPlayer.setId(-1L);
        sumPlayer.setName("合计");
        sumPlayer.setBand("");
        rowSum.setPlayer(sumPlayer);
        list.add(rowSum);
        List<DynamicColTable.Row<Object>> rows = new ArrayList<>();
        for (WaveCheckInRoomStatisticBean statisticBean : list) {
            DynamicColTable.Row<Object> row = new DynamicColTable.Row<>();
            // 昵称
            row.putFreezeCol(Objects.toString(statisticBean.getPlayer().getName(), StringUtils.EMPTY));
            // 波段号
            row.putFreezeCol(Objects.toString(statisticBean.getPlayer().getBand(), StringUtils.EMPTY));
            List<WaveCheckInUserRecordSumBean> detail = statisticBean.getDetail();
            WaveCheckInUserSumBean sum = statisticBean.getSum();
            if (Objects.equals(metrics, CheckInMetricsEnum.CHARM)) {
                // 魅力值收入明细
                checkInExportHandler.appendCharmDetailColumns(row, detail);
                // 其他统计字段
                checkInExportHandler.appendCharmStatisticSum(row, sum, true);
            } else if (Objects.equals(metrics, CheckInMetricsEnum.INCOME)) {
                // 钻石收入明细
                checkInExportHandler.appendIncomeDetailColumns(row, detail);
                // 其他统计字段
                checkInExportHandler.appendIncomeStatisticSum(row, sum);
            }
            rows.add(row);
        }
        return rows;
    }



}
